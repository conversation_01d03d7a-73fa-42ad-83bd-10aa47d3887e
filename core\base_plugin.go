package core

import (
	"context"
	"fmt"
	"time"
)

// BasePlugin 插件基础结构
type BasePlugin struct {
	config PluginConfig
}

// NewBasePlugin 创建基础插件
func NewBasePlugin(config PluginConfig) *BasePlugin {
	return &BasePlugin{
		config: config,
	}
}

// GetConfig 获取插件配置
func (p *BasePlugin) GetConfig() PluginConfig {
	return p.config
}

// Validate 验证插件配置
func (p *BasePlugin) Validate() error {
	if p.config.Name == "" {
		return fmt.Errorf("plugin name is required")
	}
	
	switch p.config.Schedule.Type {
	case ScheduleTypeCron:
		if p.config.Schedule.CronExpr == "" {
			return fmt.Errorf("cron expression is required for cron schedule")
		}
	case ScheduleTypeInterval:
		if p.config.Schedule.Interval <= 0 {
			return fmt.Errorf("interval must be positive for interval schedule")
		}
	case ScheduleTypeLunar:
		lunar := p.config.Schedule.LunarConfig
		if lunar.Day < 1 || lunar.Day > 30 {
			return fmt.Errorf("lunar day must be between 1 and 30")
		}
		if lunar.Month < 0 || lunar.Month > 12 {
			return fmt.Errorf("lunar month must be between 0 and 12")
		}
		if lunar.Hour < 0 || lunar.Hour > 23 {
			return fmt.Errorf("lunar hour must be between 0 and 23")
		}
		if lunar.Minute < 0 || lunar.Minute > 59 {
			return fmt.Errorf("lunar minute must be between 0 and 59")
		}
	default:
		return fmt.Errorf("unsupported schedule type: %s", p.config.Schedule.Type)
	}
	
	return nil
}

// Execute 执行任务（默认实现，子插件应该重写此方法）
func (p *BasePlugin) Execute(ctx context.Context) *TaskResult {
	startTime := time.Now()
	
	// 默认实现：简单的成功结果
	result := &TaskResult{
		Success:   true,
		Message:   "Base plugin executed successfully",
		StartTime: startTime,
		EndTime:   time.Now(),
		Data:      p.config.Name,
	}
	result.Duration = result.EndTime.Sub(result.StartTime)
	
	return result
}
