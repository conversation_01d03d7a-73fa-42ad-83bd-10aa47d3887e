package scheduler

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
	"crontabsystem/core"
	"crontabsystem/lunar"
	"github.com/robfig/cron/v3"
)

// TaskScheduler 任务调度器
type TaskScheduler struct {
	plugins        map[string]core.Plugin        // 插件集合
	cronScheduler  *cron.Cron                   // Cron调度器
	intervalTasks  map[string]*IntervalTask     // 间隔任务
	lunarTasks     map[string]*LunarTask        // 农历任务
	messageSender  core.MessageSender           // 消息发送器
	lunarCalc     *lunar.Calculator            // 农历计算器
	ctx           context.Context               // 上下文
	cancel        context.CancelFunc            // 取消函数
	mu            sync.RWMutex                  // 读写锁
	running       bool                          // 运行状态
	wg            sync.WaitGroup                // 等待组
}

// IntervalTask 间隔任务
type IntervalTask struct {
	plugin   core.Plugin
	ticker   *time.Ticker
	cancel   context.CancelFunc
	interval time.Duration
}

// LunarTask 农历任务
type LunarTask struct {
	plugin     core.Plugin
	config     core.LunarConfig
	nextTime   time.Time
	cancel     context.CancelFunc
}

// NewTaskScheduler 创建任务调度器
func NewTaskScheduler(messageSender core.MessageSender) *TaskScheduler {
	cronScheduler := cron.New(cron.WithSeconds()) // 支持秒级精度
	
	return &TaskScheduler{
		plugins:       make(map[string]core.Plugin),
		cronScheduler: cronScheduler,
		intervalTasks: make(map[string]*IntervalTask),
		lunarTasks:    make(map[string]*LunarTask),
		messageSender: messageSender,
		lunarCalc:     lunar.NewCalculator(),
	}
}

// AddPlugin 添加插件
func (s *TaskScheduler) AddPlugin(plugin core.Plugin) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	config := plugin.GetConfig()
	
	// 验证插件配置
	if err := plugin.Validate(); err != nil {
		return fmt.Errorf("plugin validation failed: %w", err)
	}
	
	// 检查插件是否已存在
	if _, exists := s.plugins[config.Name]; exists {
		return fmt.Errorf("plugin %s already exists", config.Name)
	}
	
	// 添加到插件集合
	s.plugins[config.Name] = plugin
	
	// 如果调度器正在运行，立即调度该插件
	if s.running && config.Enabled {
		return s.schedulePlugin(plugin)
	}
	
	return nil
}

// RemovePlugin 移除插件
func (s *TaskScheduler) RemovePlugin(name string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	plugin, exists := s.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}
	
	// 移除调度
	s.unschedulePlugin(plugin)
	
	// 从插件集合中删除
	delete(s.plugins, name)
	
	return nil
}

// Start 启动调度器
func (s *TaskScheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.running {
		return fmt.Errorf("scheduler is already running")
	}
	
	s.ctx, s.cancel = context.WithCancel(ctx)
	s.running = true
	
	// 启动Cron调度器
	s.cronScheduler.Start()
	
	// 调度所有已启用的插件
	for _, plugin := range s.plugins {
		if plugin.GetConfig().Enabled {
			if err := s.schedulePlugin(plugin); err != nil {
				log.Printf("Failed to schedule plugin %s: %v", plugin.GetConfig().Name, err)
			}
		}
	}
	
	// 启动农历任务检查器
	s.wg.Add(1)
	go s.lunarTaskChecker()
	
	log.Println("Task scheduler started")
	return nil
}

// Stop 停止调度器
func (s *TaskScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.running {
		return fmt.Errorf("scheduler is not running")
	}
	
	// 停止Cron调度器
	s.cronScheduler.Stop()
	
	// 取消所有任务
	s.cancel()
	
	// 清理间隔任务
	for _, task := range s.intervalTasks {
		task.ticker.Stop()
		if task.cancel != nil {
			task.cancel()
		}
	}
	s.intervalTasks = make(map[string]*IntervalTask)
	
	// 清理农历任务
	for _, task := range s.lunarTasks {
		if task.cancel != nil {
			task.cancel()
		}
	}
	s.lunarTasks = make(map[string]*LunarTask)
	
	s.running = false
	
	// 等待所有goroutine结束
	s.wg.Wait()
	
	log.Println("Task scheduler stopped")
	return nil
}

// GetPlugins 获取所有插件
func (s *TaskScheduler) GetPlugins() map[string]core.Plugin {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	plugins := make(map[string]core.Plugin)
	for name, plugin := range s.plugins {
		plugins[name] = plugin
	}
	return plugins
}

// schedulePlugin 调度插件
func (s *TaskScheduler) schedulePlugin(plugin core.Plugin) error {
	config := plugin.GetConfig()
	
	switch config.Schedule.Type {
	case core.ScheduleTypeCron:
		return s.scheduleCronTask(plugin)
	case core.ScheduleTypeInterval:
		return s.scheduleIntervalTask(plugin)
	case core.ScheduleTypeLunar:
		return s.scheduleLunarTask(plugin)
	default:
		return fmt.Errorf("unsupported schedule type: %s", config.Schedule.Type)
	}
}

// unschedulePlugin 取消插件调度
func (s *TaskScheduler) unschedulePlugin(plugin core.Plugin) {
	config := plugin.GetConfig()
	name := config.Name
	
	switch config.Schedule.Type {
	case core.ScheduleTypeCron:
		// Cron任务会在调度器停止时自动清理
	case core.ScheduleTypeInterval:
		if task, exists := s.intervalTasks[name]; exists {
			task.ticker.Stop()
			if task.cancel != nil {
				task.cancel()
			}
			delete(s.intervalTasks, name)
		}
	case core.ScheduleTypeLunar:
		if task, exists := s.lunarTasks[name]; exists {
			if task.cancel != nil {
				task.cancel()
			}
			delete(s.lunarTasks, name)
		}
	}
}

// scheduleCronTask 调度Cron任务
func (s *TaskScheduler) scheduleCronTask(plugin core.Plugin) error {
	config := plugin.GetConfig()
	
	_, err := s.cronScheduler.AddFunc(config.Schedule.CronExpr, func() {
		s.executeTask(plugin)
	})
	
	if err != nil {
		return fmt.Errorf("failed to schedule cron task: %w", err)
	}
	
	log.Printf("Scheduled cron task for plugin %s with expression: %s", config.Name, config.Schedule.CronExpr)
	return nil
}

// scheduleIntervalTask 调度间隔任务
func (s *TaskScheduler) scheduleIntervalTask(plugin core.Plugin) error {
	config := plugin.GetConfig()
	
	ctx, cancel := context.WithCancel(s.ctx)
	ticker := time.NewTicker(config.Schedule.Interval)
	
	task := &IntervalTask{
		plugin:   plugin,
		ticker:   ticker,
		cancel:   cancel,
		interval: config.Schedule.Interval,
	}
	
	s.intervalTasks[config.Name] = task
	
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer ticker.Stop()
		
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				s.executeTask(plugin)
			}
		}
	}()
	
	log.Printf("Scheduled interval task for plugin %s with interval: %v", config.Name, config.Schedule.Interval)
	return nil
}

// scheduleLunarTask 调度农历任务
func (s *TaskScheduler) scheduleLunarTask(plugin core.Plugin) error {
	config := plugin.GetConfig()
	lunarConfig := config.Schedule.LunarConfig
	
	// 计算下一次执行时间
	nextTime := s.lunarCalc.GetNextLunarDate(
		time.Now(),
		lunarConfig.Month,
		lunarConfig.Day,
		lunarConfig.Hour,
		lunarConfig.Minute,
	)
	
	_, cancel := context.WithCancel(s.ctx)
	
	task := &LunarTask{
		plugin:   plugin,
		config:   lunarConfig,
		nextTime: nextTime,
		cancel:   cancel,
	}
	
	s.lunarTasks[config.Name] = task
	
	log.Printf("Scheduled lunar task for plugin %s, next execution: %s", config.Name, nextTime.Format("2006-01-02 15:04:05"))
	return nil
}

// lunarTaskChecker 农历任务检查器
func (s *TaskScheduler) lunarTaskChecker() {
	defer s.wg.Done()
	
	ticker := time.NewTicker(time.Minute) // 每分钟检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.checkLunarTasks()
		}
	}
}

// checkLunarTasks 检查农历任务
func (s *TaskScheduler) checkLunarTasks() {
	now := time.Now()
	
	s.mu.RLock()
	tasks := make([]*LunarTask, 0, len(s.lunarTasks))
	for _, task := range s.lunarTasks {
		tasks = append(tasks, task)
	}
	s.mu.RUnlock()
	
	for _, task := range tasks {
		if now.After(task.nextTime) || now.Equal(task.nextTime.Truncate(time.Minute)) {
			// 执行任务
			go s.executeTask(task.plugin)
			
			// 计算下一次执行时间
			task.nextTime = s.lunarCalc.GetNextLunarDate(
				now.Add(time.Minute), // 避免重复执行
				task.config.Month,
				task.config.Day,
				task.config.Hour,
				task.config.Minute,
			)
			
			log.Printf("Lunar task %s executed, next execution: %s", 
				task.plugin.GetConfig().Name, 
				task.nextTime.Format("2006-01-02 15:04:05"))
		}
	}
}

// executeTask 执行任务
func (s *TaskScheduler) executeTask(plugin core.Plugin) {
	config := plugin.GetConfig()
	
	log.Printf("Executing task: %s", config.Name)
	
	// 创建执行上下文
	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Minute) // 默认超时10分钟
	defer cancel()
	
	// 执行插件任务
	result := plugin.Execute(ctx)
	
	log.Printf("Task %s completed: success=%t, duration=%v", 
		config.Name, result.Success, result.Duration)
	
	// 发送通知
	go s.sendNotification(config, result)
}

// sendNotification 发送通知
func (s *TaskScheduler) sendNotification(config core.PluginConfig, result *core.TaskResult) {
	if !config.Notification.Enabled {
		return
	}
	
	// 检查是否需要发送通知
	shouldNotify := false
	if result.Success && config.Notification.OnSuccess {
		shouldNotify = true
	} else if !result.Success && config.Notification.OnFailure {
		shouldNotify = true
	}
	
	if !shouldNotify {
		return
	}
	
	// 设置结果数据为插件名称（用于模板替换）
	result.Data = config.Name
	
	// 发送所有配置的通知渠道
	for _, channel := range config.Notification.Channels {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		
		err := s.messageSender.Send(ctx, channel.Type, channel.Config, channel.Template, result)
		if err != nil {
			log.Printf("Failed to send notification for plugin %s via %s: %v", config.Name, channel.Type, err)
		} else {
			log.Printf("Notification sent for plugin %s via %s", config.Name, channel.Type)
		}
		
		cancel()
	}
}
