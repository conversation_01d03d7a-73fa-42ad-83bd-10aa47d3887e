package main

import (
	"context"
	"crontabsystem/notifier"
	"crontabsystem/plugins"
	"crontabsystem/scheduler"
	"fmt"
	"log"
	"time"
)

func main() {
	fmt.Println("=== 轻量级插件化定时任务系统演示 ===")
	fmt.Println()

	// 创建简单的调度器演示
	fmt.Println("2. 插件调度系统演示（按Ctrl+C停止）：")

	// 全局配置
	emailConfig := map[string]string{
		"smtp_host": "smtp.example.com",
		"smtp_port": "587",
		"username":  "<EMAIL>",
		"password":  "password",
		"from":      "<EMAIL>",
	}

	// 创建消息发送器和调度器
	messageSender := notifier.NewUnifiedMessageSender(emailConfig)
	taskScheduler := scheduler.NewTaskScheduler(messageSender)

	// 注册演示插件
	demoPlugin := plugins.NewDemoPlugin()
	if err := taskScheduler.AddPlugin(demoPlugin); err != nil {
		log.Fatalf("Failed to add demo plugin: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second) // 运行1分钟
	defer cancel()

	// 启动调度器
	if err := taskScheduler.Start(ctx); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}

	fmt.Println("   演示插件启动成功，每10秒执行一次...")
	fmt.Println("   (将自动运行1分钟)")

	// 等待上下文超时
	<-ctx.Done()

	// 停止调度器
	if err := taskScheduler.Stop(); err != nil {
		log.Printf("Error stopping scheduler: %v", err)
	}

}
