package plugins

import (
	"context"
	"crontabsystem/core"
	"fmt"
	"time"
)

// DemoPlugin 演示插件，快速执行以展示效果
type DemoPlugin struct {
	*core.BasePlugin
}

func NewDemoPlugin() *DemoPlugin {
	config := core.PluginConfig{
		Name:        "demo_task",
		Description: "快速演示任务",
		Enabled:     true,
		Schedule: core.ScheduleConfig{
			Type:     core.ScheduleTypeCron,
			CronExpr: "*/10 * * * * *", // 每10秒执行一次
		},
		Notification: core.NotificationConfig{
			Enabled: false, // 关闭通知，避免干扰
		},
	}

	return &DemoPlugin{
		BasePlugin: core.NewBasePlugin(config),
	}
}

func (p *DemoPlugin) Execute(ctx context.Context) *core.TaskResult {
	startTime := time.Now()

	result := &core.TaskResult{
		StartTime: startTime,
		Success:   true,
		Data:      p.GetConfig().Name,
		Message:   fmt.Sprintf("演示任务执行成功 - %s", time.Now().Format("15:04:05")),
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	return result
}
