package lunar

import (
	"time"
)

// LunarDate 农历日期
type LunarDate struct {
	Year  int  `json:"year"`   // 农历年
	Month int  `json:"month"`  // 农历月
	Day   int  `json:"day"`    // 农历日
	IsLeap bool `json:"is_leap"` // 是否闰月
}

// Calculator 农历计算器
type Calculator struct {
	// 农历数据表，包含1900-2100年的农历信息
	// 每年用一个32位整数表示，低12位表示平月天数，高4位表示闰月位置和天数
	lunarInfo []int
}

// NewCalculator 创建农历计算器
func NewCalculator() *Calculator {
	return &Calculator{
		lunarInfo: []int{
			0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, // 1900-1909
			0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, // 1910-1919
			0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, // 1920-1929
			0x06566, 0x0d4a0, 0x0ea50, 0x16a95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, // 1930-1939
			0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, // 1940-1949
			0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, // 1950-1959
			0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, // 1960-1969
			0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, // 1970-1979
			0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, // 1980-1989
			0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0, // 1990-1999
			0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, // 2000-2009
			0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, // 2010-2019
			0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, // 2020-2029
			0x05aa0, 0x076a3, 0x096d0, 0x04bd7, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, // 2030-2039
			0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, // 2040-2049
			0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, // 2050-2059
			0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x15755, 0x056a0, 0x0a6d0, 0x055d4, // 2060-2069
			0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, // 2070-2079
			0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, // 2080-2089
			0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, // 2090-2099
			0x0d520, // 2100
		},
	}
}

// SolarToLunar 将公历日期转换为农历日期
func (c *Calculator) SolarToLunar(date time.Time) *LunarDate {
	// 基准日期：1900年1月31日对应农历1900年1月1日
	baseDate := time.Date(1900, 1, 31, 0, 0, 0, 0, time.UTC)
	
	// 计算天数差
	days := int(date.Sub(baseDate).Hours() / 24)
	
	// 计算农历年份
	year := 1900
	for days > 0 {
		yearDays := c.getLunarYearDays(year)
		if days >= yearDays {
			days -= yearDays
			year++
		} else {
			break
		}
	}
	
	// 计算农历月份和日期
	month := 1
	isLeap := false
	leapMonth := c.getLeapMonth(year)
	
	for days > 0 {
		monthDays := 29
		if c.isLunarBigMonth(year, month) {
			monthDays = 30
		}
		
		// 处理闰月
		if leapMonth > 0 && month == leapMonth && !isLeap {
			// 第一次遇到闰月，计算闰月天数
			if days >= monthDays {
				days -= monthDays
				isLeap = true
				monthDays = 29
				if c.isLeapBigMonth(year) {
					monthDays = 30
				}
			}
		}
		
		if days >= monthDays {
			days -= monthDays
			if isLeap {
				isLeap = false
			} else {
				month++
			}
		} else {
			break
		}
	}
	
	return &LunarDate{
		Year:  year,
		Month: month,
		Day:   days + 1,
		IsLeap: isLeap,
	}
}

// GetNextLunarDate 获取下一个指定农历日期的公历时间
func (c *Calculator) GetNextLunarDate(currentTime time.Time, month, day, hour, minute int) time.Time {
	current := c.SolarToLunar(currentTime)
	targetYear := current.Year
	
	// 如果当前日期已经过了目标日期，则计算下一年
	if month > 0 && current.Month > month {
		targetYear++
	} else if month > 0 && current.Month == month && current.Day >= day {
		targetYear++
	} else if month == 0 && current.Day >= day {
		// 每月执行，如果当前日期已过，则下个月
		if current.Month == 12 {
			targetYear++
		}
	}
	
	// 查找目标农历日期对应的公历日期
	return c.findSolarDate(targetYear, month, day, hour, minute)
}

// findSolarDate 查找指定农历日期对应的公历日期
func (c *Calculator) findSolarDate(year, month, day, hour, minute int) time.Time {
	// 从公历年初开始搜索
	start := time.Date(year, 1, 1, 0, 0, 0, 0, time.Local)
	end := time.Date(year+1, 12, 31, 23, 59, 59, 0, time.Local)
	
	// 逐日检查
	for d := start; d.Before(end); d = d.AddDate(0, 0, 1) {
		lunar := c.SolarToLunar(d)
		
		// 检查是否匹配
		if (month == 0 || lunar.Month == month) && lunar.Day == day {
			return time.Date(d.Year(), d.Month(), d.Day(), hour, minute, 0, 0, time.Local)
		}
	}
	
	// 未找到，返回下一年的第一天
	return time.Date(year+1, 1, 1, hour, minute, 0, 0, time.Local)
}

// getLunarYearDays 获取农历年的总天数
func (c *Calculator) getLunarYearDays(year int) int {
	if year < 1900 || year > 2100 {
		return 354
	}
	
	days := 0
	info := c.lunarInfo[year-1900]
	
	// 计算12个平月的天数
	for i := 0x8000; i > 0x8; i >>= 1 {
		if (info & i) != 0 {
			days += 30
		} else {
			days += 29
		}
	}
	
	// 加上闰月天数
	if c.getLeapMonth(year) > 0 {
		if c.isLeapBigMonth(year) {
			days += 30
		} else {
			days += 29
		}
	}
	
	return days
}

// getLeapMonth 获取农历年的闰月月份（0表示无闰月）
func (c *Calculator) getLeapMonth(year int) int {
	if year < 1900 || year > 2100 {
		return 0
	}
	return c.lunarInfo[year-1900] & 0xf
}

// isLunarBigMonth 判断农历平月是否为大月（30天）
func (c *Calculator) isLunarBigMonth(year, month int) bool {
	if year < 1900 || year > 2100 {
		return false
	}
	return (c.lunarInfo[year-1900] & (0x10000 >> month)) != 0
}

// isLeapBigMonth 判断农历闰月是否为大月（30天）
func (c *Calculator) isLeapBigMonth(year int) bool {
	if year < 1900 || year > 2100 {
		return false
	}
	return (c.lunarInfo[year-1900] & 0x10000) != 0
}
