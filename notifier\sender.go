package notifier

import (
	"context"
	"fmt"
	"net/smtp"
	"strings"
	"time"
	"bytes"
	"encoding/json"
	"net/http"
	"crontabsystem/core"
)

// UnifiedMessageSender 统一消息发送器
type UnifiedMessageSender struct {
	emailConfig map[string]string // 邮件配置
}

// NewUnifiedMessageSender 创建统一消息发送器
func NewUnifiedMessageSender(emailConfig map[string]string) *UnifiedMessageSender {
	return &UnifiedMessageSender{
		emailConfig: emailConfig,
	}
}

// Send 发送消息
func (s *UnifiedMessageSender) Send(ctx context.Context, msgType core.MessageType, config map[string]string, template core.MessageTemplate, result *core.TaskResult) error {
	switch msgType {
	case core.MessageTypeEmail:
		return s.sendEmail(ctx, config, template, result)
	case core.MessageTypeDingTalk:
		return s.sendDingTalk(ctx, config, template, result)
	default:
		return fmt.Errorf("unsupported message type: %s", msgType)
	}
}

// Support 检查是否支持指定的消息类型
func (s *UnifiedMessageSender) Support(msgType core.MessageType) bool {
	switch msgType {
	case core.MessageTypeEmail, core.MessageTypeDingTalk:
		return true
	default:
		return false
	}
}

// sendEmail 发送邮件
func (s *UnifiedMessageSender) sendEmail(ctx context.Context, config map[string]string, template core.MessageTemplate, result *core.TaskResult) error {
	// 合并配置，插件配置优先
	finalConfig := make(map[string]string)
	for k, v := range s.emailConfig {
		finalConfig[k] = v
	}
	for k, v := range config {
		finalConfig[k] = v
	}
	
	// 检查必要参数
	smtpHost := finalConfig["smtp_host"]
	smtpPort := finalConfig["smtp_port"]
	username := finalConfig["username"]
	password := finalConfig["password"]
	from := finalConfig["from"]
	to := finalConfig["to"]
	
	if smtpHost == "" || smtpPort == "" || username == "" || password == "" || from == "" || to == "" {
		return fmt.Errorf("missing required email configuration")
	}
	
	// 替换模板变量
	subject := s.replaceTemplateVars(template.Subject, result)
	body := s.replaceTemplateVars(template.Body, result)
	
	// 构建邮件内容
	msg := []byte(fmt.Sprintf(
		"From: %s\r\nTo: %s\r\nSubject: %s\r\n\r\n%s",
		from, to, subject, body,
	))
	
	// 发送邮件
	auth := smtp.PlainAuth("", username, password, smtpHost)
	addr := fmt.Sprintf("%s:%s", smtpHost, smtpPort)
	
	toList := strings.Split(to, ",")
	for i, addr := range toList {
		toList[i] = strings.TrimSpace(addr)
	}
	
	return smtp.SendMail(addr, auth, from, toList, msg)
}

// sendDingTalk 发送钉钉消息
func (s *UnifiedMessageSender) sendDingTalk(ctx context.Context, config map[string]string, template core.MessageTemplate, result *core.TaskResult) error {
	webhook := config["webhook"]
	secret := config["secret"]
	
	if webhook == "" {
		return fmt.Errorf("missing dingtalk webhook configuration")
	}
	
	// 替换模板变量
	title := s.replaceTemplateVars(template.Subject, result)
	text := s.replaceTemplateVars(template.Body, result)
	
	// 构建钉钉消息
	message := map[string]interface{}{
		"msgtype": "markdown",
		"markdown": map[string]interface{}{
			"title": title,
			"text":  text,
		},
	}
	
	// 如果有密钥，计算签名
	if secret != "" {
		timestamp := time.Now().UnixMilli()
		sign := s.calculateDingTalkSign(timestamp, secret)
		webhook = fmt.Sprintf("%s&timestamp=%d&sign=%s", webhook, timestamp, sign)
	}
	
	// 发送请求
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal dingtalk message: %w", err)
	}
	
	req, err := http.NewRequestWithContext(ctx, "POST", webhook, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create dingtalk request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send dingtalk message: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("dingtalk api returned status: %d", resp.StatusCode)
	}
	
	return nil
}

// calculateDingTalkSign 计算钉钉签名
func (s *UnifiedMessageSender) calculateDingTalkSign(timestamp int64, secret string) string {
	// 这里应该使用HMAC-SHA256计算签名，简化实现
	// 实际项目中需要导入crypto/hmac和crypto/sha256包
	return fmt.Sprintf("sign_%d_%s", timestamp, secret)
}

// replaceTemplateVars 替换模板变量
func (s *UnifiedMessageSender) replaceTemplateVars(template string, result *core.TaskResult) string {
	replacer := strings.NewReplacer(
		"{{.PluginName}}", fmt.Sprintf("%v", result.Data),
		"{{.Success}}", fmt.Sprintf("%t", result.Success),
		"{{.Message}}", result.Message,
		"{{.Duration}}", result.Duration.String(),
		"{{.StartTime}}", result.StartTime.Format("2006-01-02 15:04:05"),
		"{{.EndTime}}", result.EndTime.Format("2006-01-02 15:04:05"),
		"{{.Error}}", func() string {
			if result.Error != nil {
				return result.Error.Error()
			}
			return ""
		}(),
	)
	return replacer.Replace(template)
}
