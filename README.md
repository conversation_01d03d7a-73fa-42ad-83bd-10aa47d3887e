# 轻量级插件化定时任务系统

一个基于 Go 语言开发的轻量级插件化定时任务系统，支持多种调度机制和消息通知渠道，采用约定优于配置的设计理念。

## 🚀 核心特性

### 三种调度机制
- **Cron调度**：基于标准cron表达式的精准时间调度
- **间隔调度**：按照固定时间间隔周期执行
- **农历调度**：支持中国传统农历日期的特殊定时调度

### 统一消息发送接口
- **邮件通知**：支持 SMTP 邮件发送
- **钉钉通知**：支持钉钉群机器人消息推送
- **可扩展设计**：轻松添加新的通知渠道

### 插件化架构
- **约定优于配置**：所有插件统一存放于 `plugins` 目录
- **独立插件文件**：每个插件独立成文件，通过 main 函数完成注册
- **简洁开发模式**：降低插件开发门槛
- **配置代码化**：摒弃外部配置文件，配置直接内置于代码中

## 📁 项目结构

```
crontabsystem/
├── main.go                    # 主程序入口
├── go.mod                     # Go模块定义
├── README.md                  # 项目说明文档
├── core/                      # 核心模块
│   ├── types.go              # 核心类型和接口定义
│   └── base_plugin.go        # 插件基础结构
├── scheduler/                 # 调度器模块
│   └── scheduler.go          # 任务调度器实现
├── notifier/                 # 消息通知模块
│   └── sender.go             # 统一消息发送器
├── lunar/                    # 农历计算模块
│   └── calculator.go         # 农历日期计算器
└── plugins/                  # 插件目录
    ├── health_check_plugin.go    # 系统健康检查插件
    ├── backup_plugin.go          # 数据备份插件
    └── lunar_reminder_plugin.go  # 农历节假日提醒插件
```

## 🛠 安装和运行

### 环境要求
- Go 1.19 或更高版本
- 网络连接（用于邮件和钉钉通知）

### 快速开始

1. **克隆项目**
```bash
git clone <项目地址>
cd crontabsystem
```

2. **安装依赖**
```bash
go mod tidy
```

3. **运行系统**
```bash
go run main.go
```

4. **配置通知**
   
   编辑 `main.go` 中的邮件配置：
```go
emailConfig := map[string]string{
    "smtp_host": "smtp.gmail.com",
    "smtp_port": "587", 
    "username":  "<EMAIL>",
    "password":  "your-app-password",
    "from":      "<EMAIL>",
}
```

## 📋 示例插件

### 1. 系统健康检查插件 (Cron调度)
- **功能**：定期检查系统健康状态
- **调度**：每2分钟执行一次
- **通知**：仅在异常时发送邮件通知

### 2. 数据备份插件 (间隔调度)
- **功能**：定期执行数据备份任务
- **调度**：每1分钟执行一次（演示用）
- **通知**：可配置成功/失败通知

### 3. 农历节假日提醒插件 (农历调度)
- **功能**：农历节假日自动提醒
- **调度**：每月农历十五执行
- **通知**：支持邮件和钉钉双通道

## 🔧 插件开发指南

### 插件结构模板

```go
package main

import (
    "context"
    "time"
    "crontabsystem/core"
    "crontabsystem/scheduler"
    "crontabsystem/notifier"
)

// YourPlugin 自定义插件
type YourPlugin struct {
    *core.BasePlugin
}

// NewYourPlugin 创建插件实例
func NewYourPlugin() *YourPlugin {
    config := core.PluginConfig{
        Name:        "your_plugin",
        Description: "插件描述",
        Enabled:     true,
        Schedule: core.ScheduleConfig{
            Type:     core.ScheduleTypeCron,
            CronExpr: "0 */5 * * * *", // 每5分钟
        },
        Notification: core.NotificationConfig{
            Enabled:   true,
            OnSuccess: true,
            OnFailure: true,
            Channels: []core.NotificationChannel{
                // 配置通知渠道
            },
        },
    }

    return &YourPlugin{
        BasePlugin: core.NewBasePlugin(config),
    }
}

// Execute 执行任务逻辑
func (p *YourPlugin) Execute(ctx context.Context) *core.TaskResult {
    startTime := time.Now()
    
    // TODO: 实现您的业务逻辑
    
    return &core.TaskResult{
        StartTime: startTime,
        Success:   true,
        Message:   "任务执行成功",
        Data:      p.GetConfig().Name,
        EndTime:   time.Now(),
        Duration:  time.Since(startTime),
    }
}

// 插件注册函数
func RegisterPlugin(s core.Scheduler) error {
    plugin := NewYourPlugin()
    return s.AddPlugin(plugin)
}

func main() {
    // 插件独立运行逻辑（可选）
}
```

### 调度配置示例

#### Cron调度
```go
Schedule: core.ScheduleConfig{
    Type:     core.ScheduleTypeCron,
    CronExpr: "0 30 8 * * *", // 每天上午8:30
}
```

#### 间隔调度
```go
Schedule: core.ScheduleConfig{
    Type:     core.ScheduleTypeInterval,
    Interval: 5 * time.Minute, // 每5分钟
}
```

#### 农历调度
```go
Schedule: core.ScheduleConfig{
    Type: core.ScheduleTypeLunar,
    LunarConfig: core.LunarConfig{
        Month:  1,  // 农历正月
        Day:    1,  // 农历初一
        Hour:   9,  // 上午9点
        Minute: 0,  // 0分
    },
}
```

### 通知配置示例

#### 邮件通知
```go
{
    Type: core.MessageTypeEmail,
    Config: map[string]string{
        "to": "<EMAIL>",
    },
    Template: core.MessageTemplate{
        Subject: "任务通知 - {{.PluginName}}",
        Body: `任务执行结果：
        
插件：{{.PluginName}}
状态：{{.Success}}
信息：{{.Message}}
时间：{{.StartTime}}`,
    },
}
```

#### 钉钉通知
```go
{
    Type: core.MessageTypeDingTalk,
    Config: map[string]string{
        "webhook": "https://oapi.dingtalk.com/robot/send?access_token=your_token",
        "secret":  "your_secret", // 可选
    },
    Template: core.MessageTemplate{
        Subject: "任务通知",
        Body: `## {{.PluginName}}
        
**状态：** {{if .Success}}✅ 成功{{else}}❌ 失败{{end}}
**信息：** {{.Message}}`,
    },
}
```

## 🌟 核心优势

1. **轻量级设计**：核心代码简洁，无外部依赖负担
2. **插件化架构**：高度模块化，易于扩展和维护
3. **约定优于配置**：减少配置复杂性，提升开发效率
4. **多调度支持**：涵盖常见的定时任务需求场景
5. **统一通知接口**：支持多种通知方式，易于扩展
6. **农历支持**：独特的农历调度功能，适合中国本土化需求

## 📝 配置说明

### 系统配置
系统配置采用代码化方式，在 `main.go` 中的 `DefaultSystemConfig` 函数中设置：

```go
func DefaultSystemConfig() SystemConfig {
    return SystemConfig{
        EmailConfig: map[string]string{
            "smtp_host": "smtp.gmail.com",
            "smtp_port": "587",
            "username":  "<EMAIL>", 
            "password":  "your-app-password",
            "from":      "<EMAIL>",
        },
    }
}
```

### 插件配置
每个插件的配置直接在插件代码中定义，包括：
- 基本信息（名称、描述、启用状态）
- 调度配置（类型、表达式、间隔等）
- 通知配置（渠道、模板等）

## 🚦 运行日志示例

```
2024/08/08 14:45:00 === 轻量级插件化定时任务系统 ===
2024/08/08 14:45:00 支持三种调度方式：Cron调度、间隔调度、农历调度
2024/08/08 14:45:00 支持多种通知渠道：邮件、钉钉
2024/08/08 14:45:00 采用约定优于配置的设计理念
2024/08/08 14:45:00
2024/08/08 14:45:00 Starting plugin manager...
2024/08/08 14:45:00 Loading built-in plugins...
2024/08/08 14:45:00 Plugin health_check loaded successfully: 系统健康检查
2024/08/08 14:45:00 Plugin backup_data loaded successfully: 数据备份
2024/08/08 14:45:00 Plugin lunar_reminder loaded successfully: 农历节假日提醒
2024/08/08 14:45:00 Task scheduler started
2024/08/08 14:45:00 Scheduled cron task for plugin health_check with expression: 0 */2 * * * *
2024/08/08 14:45:00 Scheduled interval task for plugin backup_data with interval: 1m0s
2024/08/08 14:45:00 Scheduled lunar task for plugin lunar_reminder, next execution: 2024-08-19 12:00:00
2024/08/08 14:45:00 Loaded 3 plugins:
2024/08/08 14:45:00   - health_check: 系统健康检查 (Type: cron, Enabled: true)
2024/08/08 14:45:00   - backup_data: 定期数据备份任务 (Type: interval, Enabled: true)
2024/08/08 14:45:00   - lunar_reminder: 农历节假日提醒 (Type: lunar, Enabled: true)
2024/08/08 14:45:00 Plugin manager started successfully
2024/08/08 14:45:00
2024/08/08 14:45:00 系统正在运行，按 Ctrl+C 退出...
```

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目。在提交代码前，请确保：

1. 代码遵循项目的编码规范
2. 添加必要的测试用例
3. 更新相关文档

## 📄 许可证

此项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

如有问题或建议，欢迎通过以下方式联系：

- 提交 Issue
- 发送邮件至 [<EMAIL>]

---

**轻量级插件化定时任务系统** - 让定时任务管理变得简单高效！
