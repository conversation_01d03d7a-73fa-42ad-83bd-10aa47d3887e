package core

import (
	"context"
	"time"
)

// ScheduleType 调度类型枚举
type ScheduleType string

const (
	ScheduleTypeCron     ScheduleType = "cron"     // Cron调度
	ScheduleTypeInterval ScheduleType = "interval" // 间隔调度
	ScheduleTypeLunar    ScheduleType = "lunar"    // 农历调度
)

// MessageType 消息类型枚举
type MessageType string

const (
	MessageTypeEmail  MessageType = "email"  // 邮件通知
	MessageTypeDingTalk MessageType = "dingtalk" // 钉钉通知
)

// PluginConfig 插件配置
type PluginConfig struct {
	Name         string            `json:"name"`         // 插件名称
	Description  string            `json:"description"`  // 插件描述
	Schedule     ScheduleConfig    `json:"schedule"`     // 调度配置
	Notification NotificationConfig `json:"notification"` // 通知配置
	Enabled      bool              `json:"enabled"`      // 是否启用
}

// ScheduleConfig 调度配置
type ScheduleConfig struct {
	Type        ScheduleType `json:"type"`         // 调度类型
	CronExpr    string       `json:"cron_expr"`    // Cron表达式 (type=cron时使用)
	Interval    time.Duration `json:"interval"`     // 间隔时间 (type=interval时使用)
	LunarConfig LunarConfig  `json:"lunar_config"` // 农历配置 (type=lunar时使用)
}

// LunarConfig 农历调度配置
type LunarConfig struct {
	Month int    `json:"month"` // 农历月份 (1-12, 0表示每月)
	Day   int    `json:"day"`   // 农历日期 (1-30, 0表示每日)
	Hour  int    `json:"hour"`  // 小时 (0-23)
	Minute int   `json:"minute"` // 分钟 (0-59)
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Enabled   bool                   `json:"enabled"`   // 是否启用通知
	OnSuccess bool                   `json:"on_success"` // 成功时是否通知
	OnFailure bool                   `json:"on_failure"` // 失败时是否通知
	Channels  []NotificationChannel  `json:"channels"`  // 通知渠道
}

// NotificationChannel 通知渠道配置
type NotificationChannel struct {
	Type     MessageType       `json:"type"`     // 通知类型
	Config   map[string]string `json:"config"`   // 渠道配置
	Template MessageTemplate   `json:"template"` // 消息模板
}

// MessageTemplate 消息模板
type MessageTemplate struct {
	Subject string `json:"subject"` // 消息标题
	Body    string `json:"body"`    // 消息内容
}

// TaskResult 任务执行结果
type TaskResult struct {
	Success   bool          `json:"success"`    // 是否成功
	Message   string        `json:"message"`    // 结果消息
	Duration  time.Duration `json:"duration"`   // 执行耗时
	Error     error         `json:"error"`      // 错误信息
	Data      interface{}   `json:"data"`       // 结果数据
	StartTime time.Time     `json:"start_time"` // 开始时间
	EndTime   time.Time     `json:"end_time"`   // 结束时间
}

// Plugin 插件接口
type Plugin interface {
	// GetConfig 获取插件配置
	GetConfig() PluginConfig
	
	// Execute 执行任务
	Execute(ctx context.Context) *TaskResult
	
	// Validate 验证插件配置
	Validate() error
}

// MessageSender 消息发送接口
type MessageSender interface {
	// Send 发送消息
	Send(ctx context.Context, msgType MessageType, config map[string]string, template MessageTemplate, result *TaskResult) error
	
	// Support 检查是否支持指定的消息类型
	Support(msgType MessageType) bool
}

// Scheduler 调度器接口
type Scheduler interface {
	// AddPlugin 添加插件
	AddPlugin(plugin Plugin) error
	
	// RemovePlugin 移除插件
	RemovePlugin(name string) error
	
	// Start 启动调度器
	Start(ctx context.Context) error
	
	// Stop 停止调度器
	Stop() error
	
	// GetPlugins 获取所有插件
	GetPlugins() map[string]Plugin
}
