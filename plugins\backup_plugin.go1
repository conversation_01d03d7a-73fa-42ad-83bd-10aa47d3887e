package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
	"crontabsystem/core"
	"crontabsystem/scheduler"
	"crontabsystem/notifier"
)

// BackupPlugin 数据备份插件
type BackupPlugin struct {
	*core.BasePlugin
}

// NewBackupPlugin 创建数据备份插件
func NewBackupPlugin() *BackupPlugin {
	config := core.PluginConfig{
		Name:        "backup_data",
		Description: "定期数据备份任务",
		Enabled:     true,
		Schedule: core.ScheduleConfig{
			Type:     core.ScheduleTypeInterval,
			Interval: 30 * time.Second, // 每30秒执行一次（演示用）
		},
		Notification: core.NotificationConfig{
			Enabled:   true,
			OnSuccess: true, // 成功时通知
			OnFailure: true, // 失败时通知
			Channels: []core.NotificationChannel{
				{
					Type: core.MessageTypeDingTalk,
					Config: map[string]string{
						"webhook": "https://oapi.dingtalk.com/robot/send?access_token=your_token",
						"secret":  "your_secret", // 可选
					},
					Template: core.MessageTemplate{
						Subject: "数据备份通知",
						Body: `## 数据备份任务报告

**插件名称:** {{.PluginName}}  
**执行状态:** {{if .Success}}✅ 成功{{else}}❌ 失败{{end}}  
**执行信息:** {{.Message}}  
**开始时间:** {{.StartTime}}  
**结束时间:** {{.EndTime}}  
**执行耗时:** {{.Duration}}  

{{if not .Success}}
**错误详情:** {{.Error}}
{{end}}`,
					},
				},
			},
		},
	}

	return &BackupPlugin{
		BasePlugin: core.NewBasePlugin(config),
	}
}

// Execute 执行备份任务
func (p *BackupPlugin) Execute(ctx context.Context) *core.TaskResult {
	startTime := time.Now()
	
	result := &core.TaskResult{
		StartTime: startTime,
		Success:   true,
		Data:      p.GetConfig().Name,
	}
	
	// 模拟备份逻辑
	backupDir := "./backups"
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		result.Success = false
		result.Error = fmt.Errorf("创建备份目录失败: %w", err)
		result.Message = "备份任务失败：无法创建备份目录"
	} else {
		// 创建备份文件
		backupFile := filepath.Join(backupDir, fmt.Sprintf("backup_%s.txt", 
			time.Now().Format("20060102_150405")))
		
		file, err := os.Create(backupFile)
		if err != nil {
			result.Success = false
			result.Error = fmt.Errorf("创建备份文件失败: %w", err)
			result.Message = "备份任务失败：无法创建备份文件"
		} else {
			defer file.Close()
			
			// 写入模拟数据
			content := fmt.Sprintf("备份时间: %s\n系统信息: Windows\n数据状态: 正常\n", 
				time.Now().Format("2006-01-02 15:04:05"))
			
			if _, err := file.WriteString(content); err != nil {
				result.Success = false
				result.Error = fmt.Errorf("写入备份数据失败: %w", err)
				result.Message = "备份任务失败：无法写入数据"
			} else {
				result.Message = fmt.Sprintf("数据备份完成，备份文件: %s", backupFile)
			}
		}
	}
	
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	
	return result
}

// 插件注册函数
func RegisterPlugin(s core.Scheduler) error {
	plugin := NewBackupPlugin()
	return s.AddPlugin(plugin)
}

func main() {
	// 全局邮件配置
	emailConfig := map[string]string{
		"smtp_host": "smtp.gmail.com",
		"smtp_port": "587",
		"username":  "<EMAIL>",
		"password":  "your-app-password",
		"from":      "<EMAIL>",
	}
	
	// 创建消息发送器
	messageSender := notifier.NewUnifiedMessageSender(emailConfig)
	
	// 创建调度器
	taskScheduler := scheduler.NewTaskScheduler(messageSender)
	
	// 注册当前插件
	if err := RegisterPlugin(taskScheduler); err != nil {
		log.Fatalf("Failed to register plugin: %v", err)
	}
	
	// 启动调度器
	ctx := context.Background()
	if err := taskScheduler.Start(ctx); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}
	
	log.Println("Backup plugin started, press Ctrl+C to exit")
	
	// 等待中断信号
	select {}
}
